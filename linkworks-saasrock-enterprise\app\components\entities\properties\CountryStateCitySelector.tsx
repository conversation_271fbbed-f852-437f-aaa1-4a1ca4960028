import React, { useEffect, useState } from "react";

interface Option {
  name: string;
  code: string;
}

interface CountryStateCitySelectorProps {
  value?: { country?: string; state?: string; city?: string };
  onChange: (value: { country?: string; state?: string; city?: string }) => void;
}

export const CountryStateCitySelector: React.FC<CountryStateCitySelectorProps> = ({ value, onChange }) => {
  const [countries, setCountries] = useState<Option[]>([]);
  const [states, setStates] = useState<Option[]>([]);
  const [cities, setCities] = useState<Option[]>([]);
  const [selectedCountry, setSelectedCountry] = useState<string | undefined>(value?.country);
  const [selectedState, setSelectedState] = useState<string | undefined>(value?.state);
  const [selectedCity, setSelectedCity] = useState<string | undefined>(value?.city);

  // Fetch countries
  useEffect(() => {
    fetch("https://countriesnow.space/api/v0.1/countries/positions")
      .then((res) => res.json())
      .then((data) => {
        if (data.data) {
          setCountries(data.data.map((c: any) => ({ name: c.name, code: c.iso2 })));
        }
      });
  }, []);

  // Fetch states when country changes
  useEffect(() => {
    if (selectedCountry) {
      fetch(`https://countriesnow.space/api/v0.1/countries/states`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ country: selectedCountry }),
      })
        .then((res) => res.json())
        .then((data) => {
          setStates(data.data.states || []);
          setSelectedState(undefined);
          setCities([]);
          setSelectedCity(undefined);
        });
    }
  }, [selectedCountry]);

  // Fetch cities when state changes
  useEffect(() => {
    if (selectedCountry && selectedState) {
      fetch(`https://countriesnow.space/api/v0.1/countries/state/cities`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ country: selectedCountry, state: selectedState }),
      })
        .then((res) => res.json())
        .then((data) => {
          setCities(data.data || []);
          setSelectedCity(undefined);
        });
    }
  }, [selectedCountry, selectedState]);

  // Handle changes
  useEffect(() => {
    onChange({ country: selectedCountry, state: selectedState, city: selectedCity });
  }, [selectedCountry, selectedState, selectedCity]);

  return (
    <div style={{ display: "flex", gap: 8 }}>
      <select
        value={selectedCountry || ""}
        onChange={(e) => setSelectedCountry(e.target.value)}
      >
        <option value="">Select Country</option>
        {countries.map((c) => (
          <option key={c.code} value={c.name}>
            {c.name}
          </option>
        ))}
      </select>
      <select
        value={selectedState || ""}
        onChange={(e) => setSelectedState(e.target.value)}
        disabled={!selectedCountry}
      >
        <option value="">Select State</option>
        {states.map((s) => (
          <option key={s.name} value={s.name}>
            {s.name}
          </option>
        ))}
      </select>
      <select
        value={selectedCity || ""}
        onChange={(e) => setSelectedCity(e.target.value)}
        disabled={!selectedState}
      >
        <option value="">Select City</option>
        {cities.map((city: string) => (
          <option key={city} value={city}>
            {city}
          </option>
        ))}
      </select>
    </div>
  );
};
