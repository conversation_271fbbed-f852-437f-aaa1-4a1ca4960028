import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { PropertyType } from "~/application/enums/entities/PropertyType";
import CascadingLocationSelector from "./CascadingLocationSelector";

interface Props {
  countryValue?: string;
  stateValue?: string;
  cityValue?: string;
  onCountryChange?: (value: string) => void;
  onStateChange?: (value: string) => void;
  onCityChange?: (value: string) => void;
  showCountry?: boolean;
  showState?: boolean;
  showCity?: boolean;
  className?: string;
}

export default function CascadingLocationGroup({
  countryValue = "",
  stateValue = "",
  cityValue = "",
  onCountryChange,
  onStateChange,
  onCityChange,
  showCountry = true,
  showState = true,
  showCity = true,
  className = "",
}: Props) {
  const { t } = useTranslation();
  const [country, setCountry] = useState(countryValue);
  const [state, setState] = useState(stateValue);
  const [city, setCity] = useState(cityValue);

  // Update internal state when props change
  useEffect(() => {
    setCountry(countryValue);
  }, [countryValue]);

  useEffect(() => {
    setState(stateValue);
  }, [stateValue]);

  useEffect(() => {
    setCity(cityValue);
  }, [cityValue]);

  const handleCountryChange = (value: string) => {
    setCountry(value);
    // Reset dependent fields when country changes
    setState("");
    setCity("");
    
    onCountryChange?.(value);
    onStateChange?.("");
    onCityChange?.("");
  };

  const handleStateChange = (value: string) => {
    setState(value);
    // Reset dependent fields when state changes
    setCity("");
    
    onStateChange?.(value);
    onCityChange?.("");
  };

  const handleCityChange = (value: string) => {
    setCity(value);
    onCityChange?.(value);
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {showCountry && (
        <div>
          <label htmlFor="country" className="text-foreground/80 block text-xs font-medium">
            {t("models.property.country")}
          </label>
          <div className="mt-1">
            <CascadingLocationSelector
              type={PropertyType.COUNTRY}
              selected={country}
              onSelected={handleCountryChange}
            />
          </div>
        </div>
      )}

      {showState && (
        <div>
          <label htmlFor="state" className="text-foreground/80 block text-xs font-medium">
            {t("models.property.state")}
          </label>
          <div className="mt-1">
            <CascadingLocationSelector
              type={PropertyType.STATE}
              selected={state}
              onSelected={handleStateChange}
              countryIso={country}
              disabled={!country}
            />
          </div>
        </div>
      )}

      {showCity && (
        <div>
          <label htmlFor="city" className="text-foreground/80 block text-xs font-medium">
            {t("models.property.city")}
          </label>
          <div className="mt-1">
            <CascadingLocationSelector
              type={PropertyType.CITY}
              selected={city}
              onSelected={handleCityChange}
              countryIso={country}
              stateIso={state}
              disabled={!country || !state}
            />
          </div>
        </div>
      )}
    </div>
  );
}
