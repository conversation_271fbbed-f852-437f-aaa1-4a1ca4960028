import { LoaderFunctionArgs } from "react-router";
import { CountryStateCityApi } from "~/utils/api/.server/CountryStateCityApi";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  try {
    const countryIso = params.countryIso;
    console.log("Fetching states for country:", countryIso);

    if (!countryIso) {
      return Response.json({ error: "Country ISO code is required" }, { status: 400 });
    }

    const states = await CountryStateCityApi.getStatesByCountry(countryIso);
    console.log(`Fetched ${states.length} states for ${countryIso}`);
    return Response.json(states);
  } catch (error: any) {
    console.error("Error fetching states:", error);
    return Response.json({
      error: error.message,
      details: "Check server logs for more information"
    }, { status: 500 });
  }
};
