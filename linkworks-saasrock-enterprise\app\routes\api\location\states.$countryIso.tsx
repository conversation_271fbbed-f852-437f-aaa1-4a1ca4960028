import { LoaderFunctionArgs } from "react-router";
import { CountryStateCityApi } from "~/utils/api/.server/CountryStateCityApi";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  try {
    const countryIso = params.countryIso;
    if (!countryIso) {
      return Response.json({ error: "Country ISO code is required" }, { status: 400 });
    }

    const states = await CountryStateCityApi.getStatesByCountry(countryIso);
    return Response.json(states);
  } catch (error: any) {
    console.error("Error fetching states:", error);
    return Response.json({ error: error.message }, { status: 500 });
  }
};
