import { PropertyType } from "~/application/enums/entities/PropertyType";
import { PropertyWithDetails } from "../db/entities/entities.db.server";
import { RowValueWithDetails } from "../db/entities/rows.db.server";
import RowHelper from "./RowHelper";
import { PropertyAttributeName } from "~/application/enums/entities/PropertyAttributeName";
import { MediaDto } from "~/application/dtos/entities/MediaDto";
import { RowValueRangeDto } from "~/application/dtos/entities/RowValueRangeDto";
import PropertyFormulaValueBadge from "~/components/entities/properties/PropertyFormulaValueBadge";
import PropertyMultipleValueBadge from "~/components/entities/properties/PropertyMultipleValueBadge";
import RowBooleanCell from "~/components/entities/rows/cells/RowBooleanCell";
import RowDateCell from "~/components/entities/rows/cells/RowDateCell";
import RowTimeCell from "~/components/entities/rows/cells/RowTimeCell";
import RowMediaCell from "~/components/entities/rows/cells/RowMediaCell";
import RowNumberCell from "~/components/entities/rows/cells/RowNumberCell";
import RowRangeDateCell from "~/components/entities/rows/cells/RowRangeDateCell";
import RowRangeNumberCell from "~/components/entities/rows/cells/RowRangeNumberCell";
import { BooleanFormatType } from "../shared/BooleanUtils";
import DateUtils, { DateFormatType } from "../shared/DateUtils";
import { NumberFormatType } from "../shared/NumberUtils";
import { SelectOptionsDisplay } from "../shared/SelectOptionsUtils";
import PropertyAttributeHelper from "./PropertyAttributeHelper";
import RowSelectedOptionCell from "~/components/entities/rows/cells/RowSelectedOptionCell";
import RowLocationCell from "~/components/entities/rows/cells/RowLocationCell";
import { LocationData } from "~/types/location";
import { RowValueMultipleDto } from "~/application/dtos/entities/RowValueMultipleDto";

interface Props {
  property: PropertyWithDetails;
  value: RowValueWithDetails | null | undefined;
  withTitle?: boolean;
}
export default function RowValueUI({ property, value, withTitle }: Props) {
  const rowValue = value ? RowHelper.getDynamicPropertyValue(value, property.type) : undefined;
  if (rowValue === null || rowValue === undefined) {
    return null;
  }
  if (property.type === PropertyType.BOOLEAN) {
    const format = property.attributes.find((f) => f.name === PropertyAttributeName.FormatBoolean)?.value;
    return (
      <div className="flex items-center space-x-1">
        <RowBooleanCell value={rowValue as boolean} format={format as BooleanFormatType} />
        {withTitle && <div className="text-xs font-medium">{property.title}</div>}
      </div>
    );
  } else if (property.type === PropertyType.SELECT) {
    const display = property.attributes.find((f) => f.name === PropertyAttributeName.SelectOptions)?.value as SelectOptionsDisplay;
    return <RowSelectedOptionCell value={rowValue as string} options={property.options ?? []} display={display} />;
  } else if ([PropertyType.MULTI_SELECT, PropertyType.MULTI_TEXT].includes(property.type)) {
    return <PropertyMultipleValueBadge values={rowValue as RowValueMultipleDto[]} options={property.options ?? []} />;
  } else if ([PropertyType.RANGE_NUMBER].includes(property.type)) {
    const range = rowValue as RowValueRangeDto;
    const format = property.attributes.find((f) => f.name === PropertyAttributeName.FormatNumber)?.value as NumberFormatType;
    return <RowRangeNumberCell value={range} format={format} currencySymbol={undefined} />;
  } else if ([PropertyType.RANGE_DATE].includes(property.type)) {
    const range = rowValue as RowValueRangeDto;
    const format = property.attributes.find((f) => f.name === PropertyAttributeName.FormatDate)?.value as DateFormatType;
    return <RowRangeDateCell value={range} format={format} />;
  } else if ([PropertyType.TIME_RANGE].includes(property.type)) {
    const range = rowValue as RowValueRangeDto;
    const formatTime = (date: Date | string | null): string => {
      if (!date) return "";
      // Convert string dates to Date objects if needed
      const dateObj = date instanceof Date ? date : new Date(date);
      if (isNaN(dateObj.getTime())) return "";

      const is12HourFormat = property.subtype === "12h";
      return is12HourFormat ? DateUtils.timeHM12(dateObj) : DateUtils.timeHM(dateObj);
    };
    const startTime = formatTime(range?.dateMin);
    const endTime = formatTime(range?.dateMax);
    if (!startTime || !endTime) return <div className="text-muted-foreground">-</div>;

    // Check if it's an overnight range
    const getMinutes = (date: Date | string | null): number => {
      if (!date) return 0;
      const dateObj = date instanceof Date ? date : new Date(date);
      if (isNaN(dateObj.getTime())) return 0;
      return dateObj.getHours() * 60 + dateObj.getMinutes();
    };

    const startMinutes = getMinutes(range?.dateMin);
    const endMinutes = getMinutes(range?.dateMax);
    const isOvernight = endMinutes <= startMinutes;

    return (
      <div className="text-sm">
        {startTime} – {endTime}{isOvernight && <span className="text-muted-foreground text-xs ml-1">(+1 day)</span>}
      </div>
    );
  } else if ([PropertyType.FORMULA].includes(property.type)) {
    return <PropertyFormulaValueBadge property={property} value={rowValue} />;
  } else if (property.type === PropertyType.NUMBER) {
    const format = property.attributes.find((f) => f.name === PropertyAttributeName.FormatNumber)?.value;
    return <RowNumberCell value={rowValue as number} format={format as NumberFormatType} />;
  } else if (property.type === PropertyType.DATE) {
    const format = property.attributes.find((f) => f.name === PropertyAttributeName.FormatDate)?.value;
    return <RowDateCell value={rowValue as Date} format={format as DateFormatType} />;
  } else if (property.type === PropertyType.TIME) {
    const format = property.subtype === "12h" ? "time12" : "time";
    return <RowTimeCell value={rowValue as Date} format={format} />;
  } else if (property.type === PropertyType.MEDIA) {
    const media = rowValue as MediaDto[];
    return <RowMediaCell media={media} />;
  } else if (property.type === PropertyType.LOCATION) {
    const locationValue = rowValue as string | LocationData;
    return <RowLocationCell value={locationValue} format="address" showIcon={true} />;
  } else if ([PropertyType.COUNTRY, PropertyType.STATE, PropertyType.CITY].includes(property.type)) {
    // For cascading location properties, just display the stored value as text
    return <div className="text-sm">{rowValue as string}</div>;
  } else if (property.type === PropertyType.SELECT && rowValue) {
    const display = property.attributes.find((f) => f.name === PropertyAttributeName.SelectOptions)?.value as SelectOptionsDisplay;
    return <RowSelectedOptionCell value={rowValue as string} options={property.options ?? []} display={display} />;
  }
  let formattedValue = RowHelper.getFormattedValue(rowValue, property.type);
  if (PropertyAttributeHelper.getPropertyAttributeValue_Boolean(property, PropertyAttributeName.Password)) {
    formattedValue = "************************";
  } else if (PropertyAttributeHelper.getPropertyAttributeValue_String(property, PropertyAttributeName.Editor) === "wysiwyg") {
    return <div className="truncate" dangerouslySetInnerHTML={{ __html: formattedValue }} />;
  }
  return formattedValue;
}
