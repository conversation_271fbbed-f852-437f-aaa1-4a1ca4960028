import { LoaderFunctionArgs } from "react-router";
import { CountryStateCityApi } from "~/utils/api/.server/CountryStateCityApi";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  try {
    const { countryIso, stateIso } = params;
    if (!countryIso || !stateIso) {
      return Response.json({ error: "Country and State ISO codes are required" }, { status: 400 });
    }

    const cities = await CountryStateCityApi.getCitiesByState(countryIso, stateIso);
    return Response.json(cities);
  } catch (error: any) {
    console.error("Error fetching cities:", error);
    return Response.json({ error: error.message }, { status: 500 });
  }
};
