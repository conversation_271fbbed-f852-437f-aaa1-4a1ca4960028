import { LoaderFunctionArgs } from "react-router";
import { CountryStateCityApi } from "~/utils/api/.server/CountryStateCityApi";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  try {
    const { countryIso, stateIso } = params;
    console.log("Fetching cities for country:", countryIso, "state:", stateIso);

    if (!countryIso || !stateIso) {
      return Response.json({ error: "Country and State ISO codes are required" }, { status: 400 });
    }

    const cities = await CountryStateCityApi.getCitiesByState(countryIso, stateIso);
    console.log(`Fetched ${cities.length} cities for ${countryIso}/${stateIso}`);
    return Response.json(cities);
  } catch (error: any) {
    console.error("Error fetching cities:", error);
    return Response.json({
      error: error.message,
      details: "Check server logs for more information"
    }, { status: 500 });
  }
};
