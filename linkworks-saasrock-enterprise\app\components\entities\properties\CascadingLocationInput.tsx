import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { PropertyType } from "~/application/enums/entities/PropertyType";
import { PropertyWithDetails } from "~/utils/db/entities/entities.db.server";
import CascadingLocationSelector from "./CascadingLocationSelector";

interface Props {
  property: PropertyWithDetails;
  value?: string;
  setValue?: (value: string | undefined) => void;
  disabled?: boolean;
  // For cascading dependencies - simplified for now
  allProperties: PropertyWithDetails[];
  allValues: { [key: string]: any };
}

export default function CascadingLocationInput({
  property,
  value = "",
  setValue,
  disabled = false,
  allProperties,
  allValues,
}: Props) {
  const { t } = useTranslation();
  const [currentValue, setCurrentValue] = useState(value);

  useEffect(() => {
    setCurrentValue(value);
  }, [value]);

  const handleChange = (newValue: string) => {
    setCurrentValue(newValue);
    setValue?.(newValue);
  };

  // Get dependent values for cascading
  const getCountryValue = (): string | undefined => {
    if (property.type === PropertyType.COUNTRY) {
      return currentValue;
    }

    // Find country property in the same entity
    const countryProperty = allProperties.find(p => p.type === PropertyType.COUNTRY);
    if (countryProperty && allValues[countryProperty.name]) {
      return allValues[countryProperty.name];
    }
    return undefined;
  };

  const getStateValue = (): string | undefined => {
    if (property.type === PropertyType.STATE) {
      return currentValue;
    }

    // Find state property in the same entity
    const stateProperty = allProperties.find(p => p.type === PropertyType.STATE);
    if (stateProperty && allValues[stateProperty.name]) {
      return allValues[stateProperty.name];
    }
    return undefined;
  };

  const renderSelector = () => {
    const countryValue = getCountryValue();
    const stateValue = getStateValue();

    switch (property.type) {
      case PropertyType.COUNTRY:
        return (
          <CascadingLocationSelector
            type={PropertyType.COUNTRY}
            selected={currentValue}
            onSelected={handleChange}
            disabled={disabled}
          />
        );

      case PropertyType.STATE:
        return (
          <CascadingLocationSelector
            type={PropertyType.STATE}
            selected={currentValue}
            onSelected={handleChange}
            countryIso={countryValue}
            disabled={disabled || !countryValue}
          />
        );

      case PropertyType.CITY:
        return (
          <CascadingLocationSelector
            type={PropertyType.CITY}
            selected={currentValue}
            onSelected={handleChange}
            countryIso={countryValue}
            stateIso={stateValue}
            disabled={disabled || !countryValue || !stateValue}
          />
        );

      default:
        return <div>Unsupported location type</div>;
    }
  };

  const getHelpText = () => {
    const countryValue = getCountryValue();
    const stateValue = getStateValue();

    switch (property.type) {
      case PropertyType.COUNTRY:
        return "Select a country from the list";
      case PropertyType.STATE:
        if (!countryValue) {
          return "Please select a country first";
        }
        return "Select a state/region";
      case PropertyType.CITY:
        if (!countryValue) {
          return "Please select a country first";
        }
        if (!stateValue) {
          return "Please select a state first";
        }
        return "Select a city";
      default:
        return "";
    }
  };

  return (
    <div className="space-y-1">
      {renderSelector()}
      {getHelpText() && (
        <p className="text-xs text-gray-500">{getHelpText()}</p>
      )}
    </div>
  );
}
