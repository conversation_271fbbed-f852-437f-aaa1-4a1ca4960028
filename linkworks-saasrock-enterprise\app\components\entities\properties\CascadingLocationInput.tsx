import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { PropertyType } from "~/application/enums/entities/PropertyType";
import { PropertyWithDetails } from "~/utils/db/entities/entities.db.server";
import CascadingLocationSelector from "./CascadingLocationSelector";

interface Props {
  property: PropertyWithDetails;
  value?: string;
  setValue?: (value: string | undefined) => void;
  disabled?: boolean;
  // For cascading dependencies - simplified for now
  allProperties: PropertyWithDetails[];
  allValues: { [key: string]: any };
}

export default function CascadingLocationInput({
  property,
  value = "",
  setValue,
  disabled = false,
  allProperties,
  allValues,
}: Props) {
  const { t } = useTranslation();
  const [currentValue, setCurrentValue] = useState(value);

  useEffect(() => {
    setCurrentValue(value);
  }, [value]);

  const handleChange = (newValue: string) => {
    setCurrentValue(newValue);
    setValue?.(newValue);
  };

  // Simplified approach - for now, each property works independently
  // TODO: Implement full cascading when form context is available

  const renderSelector = () => {
    switch (property.type) {
      case PropertyType.COUNTRY:
        return (
          <CascadingLocationSelector
            type={PropertyType.COUNTRY}
            selected={currentValue}
            onSelected={handleChange}
            disabled={disabled}
          />
        );

      case PropertyType.STATE:
        return (
          <CascadingLocationSelector
            type={PropertyType.STATE}
            selected={currentValue}
            onSelected={handleChange}
            countryIso={undefined} // TODO: Get from form context
            disabled={disabled}
          />
        );

      case PropertyType.CITY:
        return (
          <CascadingLocationSelector
            type={PropertyType.CITY}
            selected={currentValue}
            onSelected={handleChange}
            countryIso={undefined} // TODO: Get from form context
            stateIso={undefined} // TODO: Get from form context
            disabled={disabled}
          />
        );

      default:
        return <div>Unsupported location type</div>;
    }
  };

  const getHelpText = () => {
    switch (property.type) {
      case PropertyType.COUNTRY:
        return "Select a country from the list";
      case PropertyType.STATE:
        return "Select a state/region (requires country selection)";
      case PropertyType.CITY:
        return "Select a city (requires country and state selection)";
      default:
        return "";
    }
  };

  return (
    <div className="space-y-1">
      {renderSelector()}
      {getHelpText() && (
        <p className="text-xs text-gray-500">{getHelpText()}</p>
      )}
    </div>
  );
}
