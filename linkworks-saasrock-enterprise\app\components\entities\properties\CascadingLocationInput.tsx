import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { PropertyType } from "~/application/enums/entities/PropertyType";
import { PropertyWithDetails } from "~/utils/db/entities/entities.db.server";
import CascadingLocationSelector from "./CascadingLocationSelector";

interface Props {
  property: PropertyWithDetails;
  value?: string;
  setValue?: (value: string | undefined) => void;
  disabled?: boolean;
  // For cascading dependencies - simplified for now
  allProperties: PropertyWithDetails[];
  allValues: { [key: string]: any };
}

export default function CascadingLocationInput({
  property,
  value = "",
  setValue,
  disabled = false,
  allProperties,
  allValues,
}: Props) {
  const { t } = useTranslation();
  const [currentValue, setCurrentValue] = useState(value);

  // Debug logging
  useEffect(() => {
    console.log(`CascadingLocationInput for ${property.name} (${property.type}):`, {
      currentValue,
      allValues,
      allProperties: allProperties.map(p => ({ name: p.name, type: p.type }))
    });
  }, [property.name, property.type, currentValue, allValues, allProperties]);

  // Force re-render when allValues change
  useEffect(() => {
    console.log(`${property.name}: Form values changed:`, allValues);
  }, [allValues, property.name]);

  useEffect(() => {
    setCurrentValue(value);
  }, [value]);

  const handleChange = (newValue: string) => {
    setCurrentValue(newValue);
    setValue?.(newValue);

    // Force re-render of dependent components by triggering a small delay
    setTimeout(() => {
      // This will cause dependent components to re-evaluate their dependencies
    }, 100);
  };

  // Get dependent values for cascading
  const getCountryValue = (): string | undefined => {
    console.log(`${property.name}: getCountryValue called. Property type:`, property.type, "Current value:", currentValue);
    if (property.type === PropertyType.COUNTRY) {
      return currentValue;
    }

    // Find country property in the same entity - try multiple approaches
    let countryProperty = allProperties.find(p => p.type === PropertyType.COUNTRY);

    // If not found by type, try by name patterns
    if (!countryProperty) {
      countryProperty = allProperties.find(p =>
        p.name.toLowerCase().includes('country') ||
        p.name.toLowerCase().includes('countrie') ||
        p.name.toLowerCase() === 'country'
      );
    }

    if (countryProperty) {
      const countryValue = allValues[countryProperty.name];
      console.log(`${property.name}: Found country property "${countryProperty.name}" with value:`, countryValue);
      console.log(`${property.name}: All form values:`, allValues);
      if (countryValue) {
        return countryValue;
      }
    }
    return undefined;
  };

  const getStateValue = (): string | undefined => {
    if (property.type === PropertyType.STATE) {
      console.log(`${property.name}: Returning own value as state:`, currentValue);
      return currentValue;
    }

    // Find state property in the same entity - try multiple approaches
    let stateProperty = allProperties.find(p => p.type === PropertyType.STATE);

    // If not found by type, try by name patterns
    if (!stateProperty) {
      stateProperty = allProperties.find(p =>
        p.name.toLowerCase().includes('state') ||
        p.name.toLowerCase().includes('province') ||
        p.name.toLowerCase() === 'state'
      );
    }

    console.log(`${property.name}: Looking for state property. Found:`, stateProperty?.name, "Type:", stateProperty?.type);

    if (stateProperty) {
      const stateValue = allValues[stateProperty.name];
      console.log(`${property.name}: State property value:`, stateValue);
      if (stateValue) {
        return stateValue;
      }
    }

    console.log(`${property.name}: No state value found. All values:`, allValues);
    return undefined;
  };

  const renderSelector = () => {
    const countryValue = getCountryValue();
    const stateValue = getStateValue();

    // Create a dependency key that changes when relevant values change
    const dependencyKey = `${countryValue || ''}-${stateValue || ''}-${currentValue || ''}`;

    switch (property.type) {
      case PropertyType.COUNTRY:
        return (
          <CascadingLocationSelector
            type={PropertyType.COUNTRY}
            selected={currentValue}
            onSelected={handleChange}
            disabled={disabled}
          />
        );

      case PropertyType.STATE:
        console.log(`${property.name}: State selector - countryValue:`, countryValue, "disabled:", disabled || !countryValue);
        return (
          <CascadingLocationSelector
            key={`state-${dependencyKey}`}
            type={PropertyType.STATE}
            selected={currentValue}
            onSelected={handleChange}
            countryIso={countryValue}
            disabled={disabled || !countryValue}
          />
        );

      case PropertyType.CITY:
        console.log(`${property.name}: City selector - countryValue:`, countryValue, "stateValue:", stateValue, "disabled:", disabled || !countryValue || !stateValue);
        return (
          <CascadingLocationSelector
            key={`city-${dependencyKey}`}
            type={PropertyType.CITY}
            selected={currentValue}
            onSelected={handleChange}
            countryIso={countryValue}
            stateIso={stateValue}
            disabled={disabled || !countryValue || !stateValue}
          />
        );

      default:
        return <div>Unsupported location type</div>;
    }
  };

  const getHelpText = () => {
    const countryValue = getCountryValue();
    const stateValue = getStateValue();

    switch (property.type) {
      case PropertyType.COUNTRY:
        return "Select a country from the list";
      case PropertyType.STATE:
        if (!countryValue) {
          return "Please select a country first";
        }
        return "Select a state/region";
      case PropertyType.CITY:
        if (!countryValue) {
          return "Please select a country first";
        }
        if (!stateValue) {
          return "Please select a state first";
        }
        return "Select a city";
      default:
        return "";
    }
  };

  return (
    <div className="space-y-1">
      {renderSelector()}
      {getHelpText() && (
        <p className="text-xs text-gray-500">{getHelpText()}</p>
      )}
    </div>
  );
}
