import { LoaderFunctionArgs } from "react-router";
import { useLoaderData } from "react-router";
import { useState } from "react";
import CascadingLocationGroup from "~/components/entities/properties/CascadingLocationGroup";

type LoaderData = {
  message: string;
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const data: LoaderData = {
    message: "Test page for cascading location properties"
  };
  return Response.json(data);
};

export default function TestLocationRoute() {
  const data = useLoaderData<LoaderData>();
  const [country, setCountry] = useState("");
  const [state, setState] = useState("");
  const [city, setCity] = useState("");

  return (
    <div className="p-8 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test Cascading Location Properties</h1>
      
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <h2 className="font-semibold text-blue-800 mb-2">Instructions:</h2>
        <ol className="text-blue-700 text-sm space-y-1">
          <li>1. Make sure you have set COUNTRY_STATE_CITY_API_KEY in your .env file</li>
          <li>2. Test the API first: <a href="/api/location/test" className="underline" target="_blank">/api/location/test</a></li>
          <li>3. Try selecting a country, then state, then city below</li>
        </ol>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h2 className="text-lg font-semibold mb-4">Cascading Location Selector</h2>
        
        <CascadingLocationGroup
          countryValue={country}
          stateValue={state}
          cityValue={city}
          onCountryChange={setCountry}
          onStateChange={setState}
          onCityChange={setCity}
        />

        <div className="mt-6 p-4 bg-gray-50 rounded">
          <h3 className="font-medium mb-2">Selected Values:</h3>
          <div className="text-sm space-y-1">
            <div><strong>Country:</strong> {country || "None"}</div>
            <div><strong>State:</strong> {state || "None"}</div>
            <div><strong>City:</strong> {city || "None"}</div>
          </div>
        </div>
      </div>

      <div className="mt-6 text-sm text-gray-600">
        <p>This test page demonstrates the cascading location functionality.</p>
        <p>Visit <code>/admin/entities</code> to create actual properties in your entities.</p>
      </div>
    </div>
  );
}
