import { LoaderFunctionArgs } from "react-router";
import { CountryStateCityApi } from "~/utils/api/.server/CountryStateCityApi";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    // Test the API integration
    const countries = await CountryStateCityApi.getCountries();
    
    // Get a sample country (United States) to test states
    const usStates = await CountryStateCityApi.getStatesByCountry("US");
    
    // Get a sample state (California) to test cities
    const caCities = await CountryStateCityApi.getCitiesByState("US", "CA");
    
    return Response.json({
      success: true,
      data: {
        countriesCount: countries.length,
        sampleCountries: countries.slice(0, 5),
        usStatesCount: usStates.length,
        sampleUsStates: usStates.slice(0, 5),
        caCitiesCount: caCities.length,
        sampleCaCities: caCities.slice(0, 10),
      },
      message: "API integration test successful"
    });
  } catch (error: any) {
    return Response.json({
      success: false,
      error: error.message,
      message: "API integration test failed"
    }, { status: 500 });
  }
};
