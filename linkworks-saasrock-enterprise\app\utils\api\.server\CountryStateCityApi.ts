// Country State City API Service
// API Documentation: https://countrystatecity.in/docs/

export interface Country {
  id: number;
  name: string;
  iso2: string;
  iso3: string;
  phonecode: string;
  capital: string;
  currency: string;
  native: string;
  emoji: string;
}

export interface State {
  id: number;
  name: string;
  iso2: string;
}

export interface City {
  id: number;
  name: string;
}

export namespace CountryStateCityApi {
  const BASE_URL = "https://api.countrystatecity.in/v1";
  
  // You need to get an API key from: https://forms.gle/ckZ8Gub6jS9sUihJA
  // For now, we'll use a placeholder - replace with your actual API key
  const API_KEY = process.env.COUNTRY_STATE_CITY_API_KEY || "YOUR_API_KEY_HERE";

  async function makeRequest(endpoint: string): Promise<any> {
    try {
      const response = await fetch(`${BASE_URL}${endpoint}`, {
        method: "GET",
        headers: {
          "X-CSCAPI-KEY": API_KEY,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error("Unauthorized: Invalid API key for Country State City API");
        }
        if (response.status === 404) {
          return [];
        }
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error("CountryStateCityApi error:", error);
      throw error;
    }
  }

  export async function getCountries(): Promise<Country[]> {
    return await makeRequest("/countries");
  }

  export async function getStatesByCountry(countryIso2: string): Promise<State[]> {
    if (!countryIso2) {
      return [];
    }
    return await makeRequest(`/countries/${countryIso2}/states`);
  }

  export async function getCitiesByState(countryIso2: string, stateIso2: string): Promise<City[]> {
    if (!countryIso2 || !stateIso2) {
      return [];
    }
    return await makeRequest(`/countries/${countryIso2}/states/${stateIso2}/cities`);
  }

  // Helper function to get country by ISO2 code
  export async function getCountryByIso2(iso2: string): Promise<Country | null> {
    const countries = await getCountries();
    return countries.find(country => country.iso2 === iso2) || null;
  }

  // Helper function to get state by ISO2 code within a country
  export async function getStateByIso2(countryIso2: string, stateIso2: string): Promise<State | null> {
    const states = await getStatesByCountry(countryIso2);
    return states.find(state => state.iso2 === stateIso2) || null;
  }

  // Helper function to get city by ID within a state
  export async function getCityById(countryIso2: string, stateIso2: string, cityId: number): Promise<City | null> {
    const cities = await getCitiesByState(countryIso2, stateIso2);
    return cities.find(city => city.id === cityId) || null;
  }
}
