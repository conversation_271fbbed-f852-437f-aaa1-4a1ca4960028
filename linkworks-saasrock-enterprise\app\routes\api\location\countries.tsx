import { LoaderFunctionArgs } from "react-router";
import { CountryStateCityApi } from "~/utils/api/.server/CountryStateCityApi";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const countries = await CountryStateCityApi.getCountries();
    return Response.json(countries);
  } catch (error: any) {
    console.error("Error fetching countries:", error);
    return Response.json({ error: error.message }, { status: 500 });
  }
};
