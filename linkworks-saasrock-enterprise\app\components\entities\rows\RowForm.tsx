import { RowWithDetails, RowWithValues } from "~/utils/db/entities/rows.db.server";
import { EntityWithDetails, PropertyWithDetails } from "~/utils/db/entities/entities.db.server";
import { Dispatch, forwardRef, Fragment, ReactNode, Ref, SetStateAction, useEffect, useImperativeHandle, useMemo, useRef, useState } from "react";
import clsx from "clsx";
import { updateItemByIdx } from "~/utils/shared/ObjectUtils";
import { useActionData, useNavigation, useParams, useSearchParams, useSubmit } from "react-router";
import { RowValueDto } from "~/application/dtos/entities/RowValueDto";
import FormGroup, { RefFormGroup } from "~/components/ui/forms/FormGroup";
import InputGroup from "~/components/ui/forms/InputGroup";
import RowHelper from "~/utils/helpers/RowHelper";
import RowValueInput, { RefRowValueInput } from "./RowValueInput";
import PropertyAttributeHelper from "~/utils/helpers/PropertyAttributeHelper";
import { PropertyAttributeName } from "~/application/enums/entities/PropertyAttributeName";
import { useTranslation } from "react-i18next";
import { EntityRelationshipWithDetails } from "~/utils/db/entities/entityRelationships.db.server";
import RowListFetcher from "../../../modules/rows/fetchers/RowListFetcher";
import SlideOverWideEmpty from "~/components/ui/slideOvers/SlideOverWideEmpty";
import { EntitiesApi } from "~/utils/api/.server/EntitiesApi";
import EntityHelper from "~/utils/helpers/EntityHelper";
import { RowsApi } from "~/utils/api/.server/RowsApi";
import { RowValueMultipleDto } from "~/application/dtos/entities/RowValueMultipleDto";
import RelationshipHelper from "~/utils/helpers/RelationshipHelper";
import RowsList from "./RowsList";
import { EntityViewWithDetails } from "~/utils/db/entities/entityViews.db.server";
import { RowDisplayDefaultProperty } from "~/utils/helpers/PropertyHelper";
import { PropertyType } from "~/application/enums/entities/PropertyType";
import { PromptFlowWithDetails } from "~/modules/promptBuilder/db/promptFlows.db.server";
import RowUrlHelper from "~/utils/helpers/RowUrlHelper";
import { setDate } from "date-fns";

export interface RefRowForm {
  save: () => void;
}

interface Props {
  entity: EntityWithDetails;
  allEntities: EntityWithDetails[];
  routes?: EntitiesApi.Routes;
  distinct?: boolean;
  isDrawer?: boolean;
  item?: RowWithDetails | null;
  editing?: boolean;
  adding?: boolean;
  onSubmit?: (formData: FormData) => void;
  canUpdate?: boolean;
  canDelete?: boolean;
  canSubmit?: boolean;
  children?: ReactNode;
  parentEntity?: EntityWithDetails;
  onCreatedRedirect?: string;
  isOnboarding?: boolean;
  onDelete?: () => void;
  relationshipRows?: RowsApi.GetRelationshipRowsData;
  hiddenProperties?: string[];
  hiddenFields?: {
    [key: string]: string | null | undefined;
  };
  state?: { loading?: boolean; submitting?: boolean };
  createdRow?: RowWithDetails;
  onCancel?: () => void;
  onChange?: (values: RowValueDto[]) => void;
  customSearchParams?: URLSearchParams;
  promptFlows?: PromptFlowWithDetails[];
  template?: { title: string; config: string } | null;
}

const parseStep = (step: any) => {
  try {
    return JSON.parse(step.block);
  } catch {
    return null;
  }
};

const RowForm = (
  {
    entity,
    routes,
    item,
    editing = false,
    isOnboarding = false,
    isDrawer = false,
    adding,
    onSubmit,
    canUpdate,
    canDelete,
    distinct,
    canSubmit = true,
    children,
    parentEntity,
    onCreatedRedirect,
    allEntities,
    onDelete,
    relationshipRows,
    hiddenProperties,
    hiddenFields,
    state,
    createdRow,
    onCancel,
    onChange,
    customSearchParams,
    promptFlows,
    template,
  }: Props,
  ref: Ref<RefRowForm>
) => {
  const { t } = useTranslation();
  const actionData = useActionData<{
    error?: string;
    message?: string;
    fieldErrors?: Record<string, string>;
    missingFields?: string[];
  }>();
  const submit = useSubmit();
  const navigation = useNavigation();
  const params = useParams();
  // const actionData = useActionData<{ newRow?: RowWithDetails }>();

  const formGroup = useRef<RefFormGroup>(null);

  const [searchParams, setSearchParams] = useSearchParams();
  const [searchingRelationshipRows, setSearchingRelationshipRows] = useState<EntityRelationshipWithDetails>();
  const [selectedRelatedEntity, setSelectedRelatedEntity] = useState<{
    entity: { slug: string; onEdit: string | null };
    view: EntityViewWithDetails | null;
    multiple: boolean;
  }>();

  // const [sliderOpen, setSliderOpen] = useState(false);
  const [sliderConfig, setSliderConfig] = useState<{
    withTitle: boolean;
    withClose: boolean;
    title: string | undefined;
    withBack: boolean;
    open: undefined | "drawer" | "sub-drawer";
    onClose: () => void;
    childClassName?: string;
  }>({
    withTitle: true,
    withClose: true,
    title: undefined as string | undefined,
    withBack: false,
    open: undefined,
    childClassName: "",
    onClose: () => {
      // setSearchingRelationshipRows(undefined);
      setSliderConfig((curr) => {
        return {
          ...curr,
          open: undefined,
        };
      });


    },
  });


  const [relatedRows, setRelatedRows] = useState<{ relationship: EntityRelationshipWithDetails; rows: RowWithValues[] }[]>([]);

  const [headers, setHeaders] = useState<RowValueDto[]>([]);

  const [childrenEntities, setChildrenEntities] = useState<{ visible: EntityRelationshipWithDetails[]; hidden: EntityRelationshipWithDetails[] }>({
    visible: [],
    hidden: [],
  });
  const [parentEntities, setParentEntities] = useState<{ visible: EntityRelationshipWithDetails[]; hidden: EntityRelationshipWithDetails[] }>({
    visible: [],
    hidden: [],
  });
  const [saveAndExit, setSaveAndExit] = useState<boolean>(false);
  const steps = entity.isOnboarding && entity.onboarding ? entity.onboarding.steps.map(parseStep).filter(Boolean) : [];
  const currentStepIndexStr = searchParams.get("step") ?? "0";
  const currentStepIndex = Number(currentStepIndexStr);
  const currentStep = steps[currentStepIndex];
  const onboardingSession = item?.onboardingSession ?? null;

  function updateSliderHeader(details: {
    withTitle: boolean;
    withClose: boolean;
    title: string | undefined;
    withBack: boolean;
    open: undefined | "drawer" | "sub-drawer";
    onClose: () => void;
    childClassName?: string;
  }) {
    setSliderConfig((curr) => {
      return {
        ...curr,
        withTitle: details.withTitle,
        withClose: details.withClose,
        title: details.title,
        withBack: details.withBack,
        onClose: details.onClose,
        open: details.open,
        childClassName: details.childClassName,
      };
    });
  }

  function resetToInitialSliderHeader() {
    setSliderConfig((curr) => {
      return {
        ...curr,
        withTitle: true,
        withClose: true,
        title: selectedRelatedEntity?.entity.title,
        open: "drawer",
        withBack: false,
        childClassName: "",
        onClose: () => {
          // setSearchingRelationshipRows(undefined);
          setSliderConfig((curr) => {
            return {
              ...curr,
              open: undefined,
            };
          });

        },
      };
    });
  }

  useEffect(() => {
    if (saveAndExit) {
      save();
    }
  }, [saveAndExit]);

  const handleNext = () => {
    save();
  };

  const handleBack = () => {
    if (currentStepIndex > 0) {
      searchParams.set("step", String(currentStepIndex - 1));
      setSearchParams(searchParams);
    }
  };

  const handleSaveForLater = () => {
    setSaveAndExit(true);
  };

  const handleComplete = () => {
    save();
  };

  useEffect(() => {
    loadInitialFields();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params.entity, params.group, customSearchParams, template]);

  useEffect(() => {
    if (onChange) {
      onChange(headers);
    }
  }, [headers, onChange]);

  // useEffect(() => {
  //   if (headers.length > 0) {
  //     rowValueInput.current?.focus();
  //   }
  // }, [headers])

  // useEffect(() => {
  //   if (actionData?.newRow && onCreated) {
  //     onCreated(actionData.newRow);
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [actionData]);

  useEffect(() => {
    if (searchingRelationshipRows?.parentId === entity.id) {
      setSelectedRelatedEntity({
        entity: searchingRelationshipRows.child,
        view: searchingRelationshipRows.childEntityView,
        multiple: true,
      });

      setSliderConfig((currDetails) => {
        return {
          ...currDetails,
          title: searchingRelationshipRows.child?.title,
          open: "drawer",
        };
      });
    } else if (searchingRelationshipRows?.childId === entity.id) {
      setSelectedRelatedEntity({
        entity: searchingRelationshipRows.parent,
        view: searchingRelationshipRows.parentEntityView,
        multiple: false,
      });

      setSliderConfig((currDetails) => {
        return {
          ...currDetails,
          title: searchingRelationshipRows.parent?.title,
          open: "drawer",
        };
      });
    }
  }, [entity.id, searchingRelationshipRows]);

  useImperativeHandle(ref, () => ({
    save,
  }));
  function save() {
    formGroup.current?.submitForm();
  }

  function loadInitialFields() {
    const initial: RowValueDto[] = [];
    if (template && !customSearchParams) {
      const config = JSON.parse(template.config);
      const defaultValues: { [key: string]: any } = {};
      Object.keys(config).forEach((key) => {
        defaultValues[key] = config[key];
      });
      customSearchParams = new URLSearchParams(defaultValues);
    }
    entity.properties
      ?.filter((f) => isPropertyVisible(f))
      .forEach((property) => {
        const existing = item?.values?.find((f) => f?.propertyId === property.id);

        let urlSearchParams = customSearchParams ?? searchParams;
        const defaultValueString =
          RowUrlHelper.getString({ urlSearchParams, property }) ??
          PropertyAttributeHelper.getPropertyAttributeValue_String(property, PropertyAttributeName.DefaultValue);
        const defaultValueNumber =
          RowUrlHelper.getNumber({ urlSearchParams, property }) ??
          PropertyAttributeHelper.getPropertyAttributeValue_Number(property, PropertyAttributeName.DefaultValue);
        const defaultValueBoolean =
          RowUrlHelper.getBoolean({ urlSearchParams, property }) ??
          PropertyAttributeHelper.getPropertyAttributeValue_Boolean(property, PropertyAttributeName.DefaultValue);

        let defaultDate = RowUrlHelper.getDate({ urlSearchParams, property }) ?? undefined;
        const defaultRange = RowUrlHelper.getRange({ urlSearchParams, property }) ?? undefined;

        const defaultMultiple = RowUrlHelper.getMultiple({ urlSearchParams, property }) ?? undefined;

        initial.push({
          propertyId: property.id,
          property: property,
          textValue: existing?.textValue ?? defaultValueString ?? undefined,
          numberValue: existing?.numberValue ? Number(existing?.numberValue) : defaultValueNumber,
          dateValue: existing?.dateValue ?? defaultDate,
          booleanValue: existing ? Boolean(existing?.booleanValue) : defaultValueBoolean,
          selectedOption: existing?.textValue ?? defaultValueString ?? undefined,
          media: existing?.media ?? [],
          multiple: existing?.multiple.sort((a: RowValueMultipleDto, b: RowValueMultipleDto) => a.order - b.order) ?? defaultMultiple ?? [],
          range: existing?.range ?? defaultRange,
        });
      });

    const relatedRows: { relationship: EntityRelationshipWithDetails; rows: RowWithValues[] }[] = [];
    if (item) {
      entity.parentEntities.forEach((relationship) => {
        if (item.parentRows?.length > 0) {
          relatedRows.push({
            relationship,
            rows: item.parentRows.filter((f) => f.relationshipId === relationship.id).map((i) => i.parent),
          });
        }
      });
      entity.childEntities.forEach((relationship) => {
        if (item.childRows?.length > 0) {
          relatedRows.push({
            relationship,
            rows: item.childRows.filter((f) => f.relationshipId === relationship.id).map((i) => i.child),
          });
        }
      });
    } else {
      entity.parentEntities.forEach((relationship) => {
        const rowId = customSearchParams?.get(relationship.parent.name) ?? searchParams.get(relationship.parent.name);
        if (rowId) {
          const foundRow = relationshipRows
            ?.filter((f) => f.relationship.id === relationship.id)
            .map((m) => m.rows)
            .flat()
            .find((f) => f.id === rowId);
          relatedRows.push({
            relationship,
            rows: foundRow ? [foundRow] : [],
          });
        }
      });
    }

    const allChildren = entity.childEntities.filter((f) => childEntityVisible(f) && allEntities.find((x) => x.id === f.childId));
    setChildrenEntities(getVisibleRelatedEntities(allChildren, relatedRows));
    const allParents = entity.parentEntities.filter((f) => f.parentId !== parentEntity?.id && allEntities.find((x) => x.id === f.parentId));
    setParentEntities(getVisibleRelatedEntities(allParents, relatedRows));

    setHeaders(initial);
    setRelatedRows(relatedRows);
  }

  function onFindEntityRows(relationship: EntityRelationshipWithDetails) {
    if (!routes) {
      return;
    }
    // setSearchingRelationshipRows(relationship);
    setSearchingRelationshipRows((curr) => {
      return {
        ...curr,
        ...relationship,
      };
    });
  }

  function addRelationshipRow(relationship: EntityRelationshipWithDetails, rows: RowWithDetails[]) {
    const newRelatedRows = [...relatedRows];
    const existing = newRelatedRows.find((f) => f.relationship.id === relationship.id);
    if (existing) {
      if (relationship.parentId === entity.id) {
        const nonExistingRows = rows.filter((f) => !existing.rows.find((ff) => ff.id === f.id));
        existing.rows = [...existing.rows, ...nonExistingRows];
      } else {
        existing.rows = rows;
      }
    } else {
      newRelatedRows.push({ relationship, rows });
    }
    setRelatedRows(newRelatedRows);
  }

  function setRelationshipRows(relationship: EntityRelationshipWithDetails, rows: RowWithDetails[]) {
    const newRelatedRows = [...relatedRows];
    const existing = newRelatedRows.find((f) => f.relationship.id === relationship.id);
    if (existing) {
      existing.rows = rows;
    } else {
      newRelatedRows.push({ relationship, rows });
    }
    setRelatedRows(newRelatedRows);
  }

  function onRemoveRelatedRow(relationship: EntityRelationshipWithDetails, row: RowWithValues) {
    const newRelatedRows = [...relatedRows];
    const existing = newRelatedRows.find((f) => f.relationship.id === relationship.id);
    if (existing) {
      existing.rows = existing.rows.filter((f) => f.id !== row.id);
    }
    setRelatedRows(newRelatedRows);
  }

  function submitForm(formData: FormData) {
    if (onSubmit) {
      onSubmit(formData);
    } else {
      submit(formData, {
        method: "post",
      });
    }
  }

  function isPropertyVisible(f: PropertyWithDetails) {
    if (f.isHidden || (!item && !f.showInCreate) || (!item && f.isReadOnly) || (item && editing && f.isReadOnly)) {
      return false;
    } else if (hiddenProperties?.includes(f.name)) {
      return false;
    }
    if (item && editing && !f.canUpdate) {
      return false;
    }

    return true;
  }

  function childEntityVisible(f: EntityRelationshipWithDetails) {
    if (f.readOnly) {
      if (!item) {
        return false;
      }
      if (item && editing) {
        return false;
      }
    }
    return true;
  }

  function canSubmitForm() {
    if (!canSubmit) {
      return false;
    }
    const required = headers.filter((f) => f.property.isRequired);
    let hasError = false;
    required.forEach((f) => {
      if (f.property.type === PropertyType.MEDIA) {
        if (f.media?.length === 0) {
          hasError = true;
        }
      }
    });
    return !hasError;
  }

  function isAddingOrEditing() {
    if (adding) {
      return true;
    }
    if (editing && item && canUpdate && navigation.state === "idle") {
      return true;
    }
    return false;
  }

  function onSaveIfAllSet() {
    return;
    // if (item) {
    //   return;
    // }
    // const missingValues = headers
    //   .filter((f) => isPropertyVisible(f.property))
    //   .map((header) => {
    //     if ([PropertyType.TEXT, PropertyType.SELECT].includes(header.property.type) && !header.textValue) {
    //       return header;
    //     } else if ([PropertyType.NUMBER].includes(header.property.type) && !header.numberValue) {
    //       return header;
    //     } else if ([PropertyType.DATE].includes(header.property.type) && !header.dateValue) {
    //       return header;
    //     }
    //     // else if ([PropertyType.MEDIA].includes(header.property.type) && (!header.media || header.media.length === 0)) {
    //     //   return header;
    //     // }
    //     else if ([PropertyType.MULTI_SELECT].includes(header.property.type) && (!header.multiple || header.multiple.length === 0)) {
    //       return header;
    //     }
    //     return null;
    //   });
    // const rowValues = missingValues.filter((f) => f !== null);

    // if (rowValues.length === 0) {
    //   formGroup.current?.submitForm();
    // }
  }

  return (
    <>
      <FormGroup
        ref={formGroup}
        id={item?.id}
        editing={editing}
        entity={entity}
        canDelete={canDelete}
        onSubmit={submitForm}
        onDelete={onDelete}
        canUpdate={canUpdate}
        canSubmit={canSubmit}
        onCancel={onCancel}
        submitDisabled={!canSubmitForm()}
        onCreatedRedirect={onCreatedRedirect}
        deleteRedirect={EntityHelper.getRoutes({ routes, entity })?.list}
        state={state}
        steps={steps}
        currentStepIndex={currentStepIndex}
        completedStepIndex={onboardingSession?.currentStepIndex || 0}
        currentStep={currentStep}
        isDrawer={isDrawer}
        handleBack={handleBack}
        handleNext={handleNext}
        handleComplete={handleComplete}
        handleSaveForLater={handleSaveForLater}
        message={
          createdRow
            ? { success: t("shared.created") + ": " + RowHelper.getTextDescription({ entity, item: createdRow, t, defaultsToFolio: true }) }
            : undefined
        }
      >
        {hiddenFields &&
          Object.keys(hiddenFields).map((f) => {
            return <Fragment key={f}>{hiddenFields[f] && <input type="hidden" name={f} value={hiddenFields[f] ?? ""} hidden readOnly />}</Fragment>;
          })}
        {!onSubmit && (
          <>
            {!item ? (
              <input type="hidden" name="redirect" value={EntityHelper.getRoutes({ routes, entity })?.list} hidden readOnly />
            ) : (
              <input type="hidden" name="redirect" value={EntityHelper.getRoutes({ routes, entity, item })?.overview} hidden readOnly />
            )}
          </>
        )}
        {entity.isOnboarding ? (
          <>
            <input type="hidden" name="currentStepIndex" value={currentStepIndex} hidden readOnly />
            <input type="hidden" name="saveAndExit" value={String(saveAndExit)} hidden readOnly />
          </>
        ) : null}
        {onCreatedRedirect && <input type="hidden" name="onCreatedRedirect" value={onCreatedRedirect} hidden readOnly />}
        <RowGroups
          item={item}
          entity={entity}
          rowValues={headers}
          parentEntity={parentEntity}
          allEntities={allEntities}
          relatedRows={relatedRows}
          editing={editing}
          canUpdate={canUpdate}
          routes={routes}
          relationshipRows={relationshipRows}
          setHeaders={setHeaders}
          addRelationshipRow={addRelationshipRow}
          setRelationshipRows={setRelationshipRows}
          onFindEntityRows={onFindEntityRows}
          onRemoveRelatedRow={onRemoveRelatedRow}
          requiredErrorList={actionData?.missingFields}
          isPropertyVisible={isPropertyVisible}
          children={children}
          canSubmit={canSubmit}
          isAddingOrEditing={isAddingOrEditing()}
          parentEntities={{
            visible: parentEntities.visible,
            hidden: parentEntities.hidden,
            onAddParentEntity: (rel) => {
              setParentEntities((prev) => {
                return {
                  visible: [...prev.visible, rel],
                  hidden: prev.hidden.filter((f) => f.id !== rel.id),
                };
              });
            },
          }}
          promptFlows={promptFlows}
          onSaveIfAllSet={onSaveIfAllSet}
          steps={steps}
          currentStepIndex={currentStepIndex}
          isDrawer={isDrawer} 
        />
        {(!entity?.onboarding) ||
          (entity?.onboarding && currentStepIndex + 1 === entity?.onboarding?.steps?.length) ? (
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-12">
            {childrenEntities.visible.map((relationship) => (
              <div key={relationship.id} className="col-span-12">
                <div className="w-full rounded-[12px] border-t-[1px] border-r-[1px] border-b-[1px] border-l-[1px] border-solid border-input bg-white p-4 shadow-lg space-y-2">
                  <h3 className="text-foreground text-sm leading-3 font-medium">
                    <div className="flex items-center space-x-1">
                      <div>
                        <span className="font-bold text-sm text-[#0A0501]">
                        {t(RelationshipHelper.getTitle({ fromEntityId: entity.id, relationship }))}
                        </span>
                        {/* {relationship.required && <span className="ml-1 text-red-500">*</span>} */}
                      </div>
                    </div>
                  </h3>
                  <RelationshipSelector
                    fromEntity={entity}
                    type="child"
                    relationship={relationship}
                    relatedRows={relatedRows}
                    onFindEntityRows={onFindEntityRows}
                    allEntities={allEntities}
                    onRemoveRelatedRow={onRemoveRelatedRow}
                    readOnly={item?.id !== undefined && (!editing || !canUpdate)}
                    routes={routes}
                    relationshipRows={relationshipRows}
                    addRelationshipRow={addRelationshipRow}
                    setRelationshipRows={setRelationshipRows}
                  />
                </div>
              </div>
            ))}

            {isAddingOrEditing() && (
              <AddHiddenRelationshipEntities
                items={childrenEntities.hidden}
                onClick={(rel) => {
                  setChildrenEntities((prev) => {
                    return {
                      visible: [...prev.visible, rel],
                      hidden: prev.hidden.filter((f) => f.id !== rel.id),
                    };
                  });
                }}
                type="child"
              />
            )}
          </div>
        ) : null}
        {relatedRows.map(({ relationship, rows }) => (
          <Fragment key={relationship.id}>
            {rows.map((row) => (
              // <>
              <input
                key={row.id}
                type="hidden"
                readOnly
                hidden
                name={`${relationship.childId === entity.id ? `parents[${relationship.parent.name}]` : `children[${relationship.child.name}]`}`}
                value={row.id}
              />
              // </>
            ))}
          </Fragment>
        ))}
      </FormGroup>
      {/* // <OpenModal className="sm:max-w-4xl" onClose={() => setSearchingRelationshipRows(undefined)}> */}
      <SlideOverWideEmpty
        withTitle={sliderConfig.withTitle}
        withClose={sliderConfig.withClose}
        title={sliderConfig.title}
        open={!!sliderConfig.open && !!selectedRelatedEntity && !!searchingRelationshipRows}
        onClose={sliderConfig.onClose}
        withBack={sliderConfig.withBack}
        childClassName={sliderConfig.childClassName}
      >
        {selectedRelatedEntity && searchingRelationshipRows && (
          <RowListFetcher
            updateSliderHeader={updateSliderHeader}
            setSliderConfig={setSliderConfig}
            resetToInitialSliderHeader={resetToInitialSliderHeader} // Use the same close handler
            currentView={selectedRelatedEntity.view}
            listUrl={EntityHelper.getRoutes({ routes, entity: selectedRelatedEntity.entity })?.list + "?view=null"}
            newUrl={EntityHelper.getRoutes({ routes, entity: selectedRelatedEntity.entity })?.new ?? ""}
            parentEntity={entity}
            onSelected={(rows) => {
              addRelationshipRow(searchingRelationshipRows, rows);
              setSearchingRelationshipRows(undefined);
            }}
            multipleSelection={selectedRelatedEntity.multiple}
            allEntities={allEntities}
            distinct={searchingRelationshipRows.distinct}
            onClose={sliderConfig.onClose}
          />
        )}
      </SlideOverWideEmpty>
      {/* // </OpenModal> */}
    </>
  );
};

function RelationshipSelector({
  fromEntity,
  routes,
  type,
  relationship,
  relatedRows,
  onFindEntityRows,
  className,
  allEntities,
  onRemoveRelatedRow,
  readOnly,
  relationshipRows,
  addRelationshipRow,
  setRelationshipRows,
}: {
  fromEntity: EntityWithDetails;
  type: "child" | "parent";
  relationship: EntityRelationshipWithDetails;
  relatedRows: { relationship: EntityRelationshipWithDetails; rows: RowWithValues[] }[];
  onFindEntityRows: (relationship: EntityRelationshipWithDetails) => void;
  className?: string;
  allEntities: EntityWithDetails[];
  onRemoveRelatedRow: (relationship: EntityRelationshipWithDetails, row: RowWithValues) => void;
  readOnly: boolean;
  routes?: EntitiesApi.Routes;
  relationshipRows?: RowsApi.GetRelationshipRowsData;
  addRelationshipRow: (relationship: EntityRelationshipWithDetails, rows: RowWithDetails[]) => void;
  setRelationshipRows: (relationship: EntityRelationshipWithDetails, rows: RowWithDetails[]) => void;
}) {
  const { t } = useTranslation();
  const [entity] = useState(
    type === "parent"
      ? {
          entity: getChildEntity(relationship)!,
          view: relationship.parentEntityView,
        }
      : {
          entity: getParentEntity(relationship)!,
          view: relationship.childEntityView,
        }
  );

  function getRows(relationship: EntityRelationshipWithDetails) {
    const existing = relatedRows.find((f) => f.relationship.id === relationship.id);
    return existing?.rows.sort((a, b) => (a.order ?? 0) - (b.order ?? 0)) ?? [];
  }
  function getParentEntity(relationship: EntityRelationshipWithDetails) {
    return allEntities.find((f) => f.id === relationship.childId);
  }
  function getChildEntity(relationship: EntityRelationshipWithDetails) {
    return allEntities.find((f) => f.id === relationship.parentId);
  }
  return (
    <div className={className}>
      {/* <div>selectedRow: {getSelectedRow()}</div>
      <div>
        options:{" "}
        {getOptions()
          .map((f) => f.value)
          .join(",")}
      </div>
      <div>
        {relatedRows.filter((f) => f.relationship.id === relationship.id).length > 0 &&
          relatedRows
            .filter((f) => f.relationship.id === relationship.id)[0]
            .rows.map((f) => f.id)
            .join(",")}
      </div> */}
      {/* {RelationshipHelper.getInputType({ fromEntityId: fromEntity.id, relationship }) === "single-select" ? (
        <InputSelector
          className="mt-1"
          name={relationship.parent.name}
          disabled={readOnly}
          value={getSelectedRow()}
          options={getOptions()}
          setValue={(value) => {
            const row = relationshipRows?.find((f) => f.relationship.id === relationship.id)?.rows.find((f) => f.id === value);
            if (row) {
              setRelationshipRows(relationship, [row]);
            }
          }}
        />
      ) : RelationshipHelper.getInputType({ fromEntityId: fromEntity.id, relationship }) === "multi-select" ? (
        <> */}
      {getRows(relationship).length === 0 ? (
        <div
          // onClick={() => onFindEntityRows(relationship)}
          // type="button"
          // disabled={readOnly}
          className={clsx(
            "border-input relative block w-full rounded-lg border-2 border-dashed p-4 text-center",
            readOnly ? "bg-background cursor-not-allowed" : "bg-background hover:border-input focus:ring-2 focus:ring-gray-500 focus:outline-none"
          )}
        >
          <span className="text-muted-foreground flex items-center space-x-1 text-xs font-normal">
            {readOnly ? (
              <div>{t("shared.notSet")}</div>
            ) : (
              <>
                {type === "parent" && (
                  <>
                    <div className="flex w-full flex-col justify-between sm:flex-row">
                      <div className="flex flex-col items-center gap-1 text-xs font-normal text-foreground sm:flex-row">
                        <div>{t("shared.no")}</div>
                        <div className="lowercase">{t(relationship.type === "one-to-one" ? relationship.parent.title : relationship.parent.titlePlural)}</div>
                        <div>
                          {relationship.type === "one-to-one" ? "details has" : "details have"} {relationship.distinct ? "been added yet" : "been selected yet"}
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          onFindEntityRows(relationship);
                        }}
                       className="inline-flex items-center justify-center gap-2 whitespace-nowrap h-8 px-3 py-0 text-xs rounded-[4px] bg-white text-foreground border border-input hover:bg-gray-50 shadow-[0px_2px_0px_0px_rgba(0,0,0,0.02)] font-normal focus:ring-input focus:ring-1 focus:outline-none cursor-pointer"
                        >
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 17 17"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="w-4 h-4"
                          >
                            <path
                              d="M9.20052 5.16536H7.86719V7.83203H5.20052V9.16536H7.86719V11.832H9.20052V9.16536H11.8672V7.83203H9.20052V5.16536ZM8.53385 1.83203C4.85385 1.83203 1.86719 4.8187 1.86719 8.4987C1.86719 12.1787 4.85385 15.1654 8.53385 15.1654C12.2139 15.1654 15.2005 12.1787 15.2005 8.4987C15.2005 4.8187 12.2139 1.83203 8.53385 1.83203ZM8.53385 13.832C5.59385 13.832 3.20052 11.4387 3.20052 8.4987C3.20052 5.5587 5.59385 3.16536 8.53385 3.16536C11.4739 3.16536 13.8672 5.5587 13.8672 8.4987C13.8672 11.4387 11.4739 13.832 8.53385 13.832Z"
                              fill="currentColor"
                            />
                          </svg>
                        <span>Add Details</span>
                      </button>
                    </div>
                  </>
                )}

                {type === "child" && (
                  <>
                    <div className="flex w-full flex-col justify-between sm:flex-row">
                      <div className="flex flex-col items-center gap-1 text-xs font-normal text-foreground sm:flex-row">
                        <div>{t("shared.no")}</div>
                        <div className="lowercase">{t(relationship.type === "one-to-one" ? relationship.child.title : relationship.child.titlePlural)}</div>
                        <div>
                          {relationship.type === "one-to-one" ? "has" : "have"} {relationship.distinct ? "been added" : "been selected"}
                        </div>
                      </div>
                       <button
                          type="button"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            onFindEntityRows(relationship);
                          }}
                          className="inline-flex items-center justify-center gap-2 whitespace-nowrap h-8 px-3 py-0 text-xs rounded-[4px] bg-white text-foreground border border-input hover:bg-gray-50 shadow-[0px_2px_0px_0px_rgba(0,0,0,0.02)] font-normal focus:ring-input focus:ring-1 focus:outline-none cursor-pointer"
                        >
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 17 17"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="w-4 h-4"
                          >
                            <path
                              d="M9.20052 5.16536H7.86719V7.83203H5.20052V9.16536H7.86719V11.832H9.20052V9.16536H11.8672V7.83203H9.20052V5.16536ZM8.53385 1.83203C4.85385 1.83203 1.86719 4.8187 1.86719 8.4987C1.86719 12.1787 4.85385 15.1654 8.53385 15.1654C12.2139 15.1654 15.2005 12.1787 15.2005 8.4987C15.2005 4.8187 12.2139 1.83203 8.53385 1.83203ZM8.53385 13.832C5.59385 13.832 3.20052 11.4387 3.20052 8.4987C3.20052 5.5587 5.59385 3.16536 8.53385 3.16536C11.4739 3.16536 13.8672 5.5587 13.8672 8.4987C13.8672 11.4387 11.4739 13.832 8.53385 13.832Z"
                              fill="currentColor"
                            />
                          </svg>
                          <span>Add Details</span>
                        </button>
                    </div>
                  </>
                )}
              </>
            )}
          </span>
        </div>
      ) : (
        <div className="relative space-y-2 overflow-visible">
          <RowsList
            entity={entity.entity}
            items={getRows(relationship) as RowWithDetails[]}
            currentView={entity.view}
            view={(entity.view?.layout ?? "card") as "table" | "board" | "grid" | "card"}
            readOnly={readOnly}
            onRemove={readOnly ? undefined : (row) => onRemoveRelatedRow(relationship, row)}
            ignoreColumns={!readOnly ? [RowDisplayDefaultProperty.FOLIO, "parent." + relationship.parent.name, "child." + relationship.child.name] : []}
            routes={routes}
          />
          <div
            className={clsx(
              "border-input relative block w-full rounded-lg border-2 border-dashed p-4 text-center",
              readOnly ? "bg-background cursor-not-allowed" : "bg-background hover:border-input focus:ring-2 focus:ring-gray-500 focus:outline-none"
            )}
          >
            <span className="text-muted-foreground flex items-center space-x-1 text-xs font-normal">
              {readOnly ? (
                <div>{t("shared.notSet")}</div>
              ) : (
                <>
                  {type === "parent" && (
                    <>
                      <div className="flex w-full flex-col justify-end sm:flex-row">
                        <button
                          type="button"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            onFindEntityRows(relationship);
                          }}
                          className="inline-flex items-center justify-center gap-2 whitespace-nowrap h-8 px-3 py-0 text-xs rounded-[4px] bg-white text-foreground border border-input hover:bg-gray-50 shadow-[0px_2px_0px_0px_rgba(0,0,0,0.02)] font-normal focus:ring-input focus:ring-1 focus:outline-none cursor-pointer"
                        >
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 17 17"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="w-4 h-4"
                          >
                            <path
                              d="M9.20052 5.16536H7.86719V7.83203H5.20052V9.16536H7.86719V11.832H9.20052V9.16536H11.8672V7.83203H9.20052V5.16536ZM8.53385 1.83203C4.85385 1.83203 1.86719 4.8187 1.86719 8.4987C1.86719 12.1787 4.85385 15.1654 8.53385 15.1654C12.2139 15.1654 15.2005 12.1787 15.2005 8.4987C15.2005 4.8187 12.2139 1.83203 8.53385 1.83203ZM8.53385 13.832C5.59385 13.832 3.20052 11.4387 3.20052 8.4987C3.20052 5.5587 5.59385 3.16536 8.53385 3.16536C11.4739 3.16536 13.8672 5.5587 13.8672 8.4987C13.8672 11.4387 11.4739 13.832 8.53385 13.832Z"
                              fill="currentColor"
                            />
                          </svg>
                          <span>Add Details</span>
                        </button>
                      </div>
                    </>
                  )}

                  {type === "child" && (
                    <>
                      <div className="flex w-full flex-col justify-end sm:flex-row">
                        <button
                          type="button"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            onFindEntityRows(relationship);
                          }}
                          className="inline-flex items-center justify-center gap-2 whitespace-nowrap h-8 px-3 py-0 text-xs rounded-[4px] bg-white text-foreground border border-input hover:bg-gray-50 shadow-[0px_2px_0px_0px_rgba(0,0,0,0.02)] font-normal focus:ring-input focus:ring-1 focus:outline-none cursor-pointer"
                        >
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 17 17"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="w-4 h-4"
                          >
                            <path
                              d="M9.20052 5.16536H7.86719V7.83203H5.20052V9.16536H7.86719V11.832H9.20052V9.16536H11.8672V7.83203H9.20052V5.16536ZM8.53385 1.83203C4.85385 1.83203 1.86719 4.8187 1.86719 8.4987C1.86719 12.1787 4.85385 15.1654 8.53385 15.1654C12.2139 15.1654 15.2005 12.1787 15.2005 8.4987C15.2005 4.8187 12.2139 1.83203 8.53385 1.83203ZM8.53385 13.832C5.59385 13.832 3.20052 11.4387 3.20052 8.4987C3.20052 5.5587 5.59385 3.16536 8.53385 3.16536C11.4739 3.16536 13.8672 5.5587 13.8672 8.4987C13.8672 11.4387 11.4739 13.832 8.53385 13.832Z"
                              fill="currentColor"
                            />
                          </svg>
                          <span>Add Details</span>
                        </button>
                      </div>
                    </>
                  )}
                </>
              )}
            </span>
          </div>
          {/* {getRows(relationship).map((item) => (
                <div
                  key={item.id}
                  className={clsx(
                    "group relative w-full overflow-visible truncate rounded-md border border-border px-4 py-3 text-left text-sm",
                    !readOnly ? "bg-background hover:border-border" : "bg-secondary/90"
                  )}
                >
                  <button
                    onClick={() => onRemoveRelatedRow(relationship, item)}
                    type="button"
                    disabled={readOnly}
                    className={clsx(
                      "absolute right-0 top-0 mr-2 mt-2 hidden origin-top-right justify-center rounded-full bg-background text-muted-foreground",
                      readOnly ? "cursor-not-allowed" : "hover:text-red-500 group-hover:flex"
                    )}
                  >
                    <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M15 12H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </button>
                  <div className="grid grid-cols-2 gap-1">
                    <div>{RowHelper.getTextDescription({ entity, item, t, defaultsToFolio: true })}</div>
                  </div>
                </div>
              ))} */}
          {/* <div className="flex justify-end gap-1 pt-2">
            <button
              onClick={() => onFindEntityRows(relationship)}
              type="button"
              className={clsx(
                "hover:bg-secondary border-border text-muted-foreground hover:border-border relative flex space-x-1 rounded-md border border-dashed px-2 py-1 text-center text-xs focus:ring-1 focus:ring-gray-500 focus:outline-hidden",
                readOnly && "hidden"
              )}
            >
              {type === "parent" && (
                <>
                  <div className="text-body leading-5 font-medium text-black">
                    {t(relationship.type === "one-to-one" ? "Change" : relationship.distinct ? "shared.add" : "Add")}
                  </div>
                  <div className="text-body leading-5 font-medium text-black lowercase">
                    {t(relationship.type === "one-to-one" ? relationship.parent.title : relationship.parent.titlePlural)}
                  </div>
                </>
              )}
              {type === "child" && (
                <>
                  <div className="text-body leading-5 font-medium text-black">
                    {t(relationship.type === "one-to-one" ? "Add" : relationship.distinct ? "shared.add" : "Add")}
                  </div>
                  <div className="text-body leading-5 font-medium text-black lowercase">
                    {t(relationship.type === "one-to-one" ? relationship.child.title : relationship.child.titlePlural)}
                  </div>
                </>
              )}
            </button>
          </div> */}
        </div>
      )}
      {/* </>
      ) : null} */}
    </div>
  );
}

function RowGroups({
  item,
  entity,
  rowValues,
  parentEntity,
  allEntities,
  relatedRows,
  editing,
  canUpdate,
  routes,
  relationshipRows,
  setHeaders,
  addRelationshipRow,
  setRelationshipRows,
  onFindEntityRows,
  onRemoveRelatedRow,
  isPropertyVisible,
  children,
  canSubmit,
  isAddingOrEditing,
  parentEntities,
  promptFlows,
  onSaveIfAllSet,
  requiredErrorList,
  steps = [],
  currentStepIndex = null,
  isDrawer,
}: {
  item?: RowWithDetails | null;
  steps: any[];
  currentStepIndex: number | null;
  entity: EntityWithDetails;
  rowValues: RowValueDto[];
  parentEntity?: EntityWithDetails;
  allEntities: EntityWithDetails[];
  relatedRows: { relationship: EntityRelationshipWithDetails; rows: RowWithValues[] }[];
  editing?: boolean;
  canUpdate?: boolean;
  routes?: EntitiesApi.Routes;
  relationshipRows?: RowsApi.GetRelationshipRowsData;
  setHeaders: Dispatch<SetStateAction<RowValueDto[]>>;
  addRelationshipRow: (relationship: EntityRelationshipWithDetails, rows: RowWithDetails[]) => void;
  setRelationshipRows: (relationship: EntityRelationshipWithDetails, rows: RowWithDetails[]) => void;
  onFindEntityRows: (relationship: EntityRelationshipWithDetails) => void;
  onRemoveRelatedRow: (relationship: EntityRelationshipWithDetails, row: RowWithValues) => void;
  isPropertyVisible: (property: PropertyWithDetails) => boolean;
  children?: ReactNode;
  canSubmit?: boolean;
  isAddingOrEditing: boolean;
  requiredErrorList?:string[];
  parentEntities: {
    visible: EntityRelationshipWithDetails[];
    hidden: EntityRelationshipWithDetails[];
    onAddParentEntity: (item: EntityRelationshipWithDetails) => void;
  };
  promptFlows?: PromptFlowWithDetails[];
  onSaveIfAllSet: () => void;
  isDrawer?: boolean; 
}) {
  const { t } = useTranslation();
  const rowValueInput = useRef<RefRowValueInput>(null);
  const [filledFieldsCounts, setFilledFieldsCounts] = useState<Record<string, number>>({});

  const groups = useMemo(() => {
    const groups: { group?: string; headers: RowValueDto[] }[] = [];
    rowValues.forEach((header) => {
      const groupName = PropertyAttributeHelper.getPropertyAttributeValue_String(header.property, PropertyAttributeName.Group);
      let found = groups.find((f) => f.group === groupName);
      if (!found) {
        found = groups.find((f) => !f.group && !groupName);
      }
      // To show only step configured fields, filtering rest of the fileds
      if (entity.isOnboarding) {
        const isStepBasedFlow = !!entity.isOnboarding && steps?.length > 0 && currentStepIndex !== null;
        const currentStep = isStepBasedFlow && currentStepIndex > -1 ? steps[currentStepIndex] : null;
        const currentStepProperites = currentStep?.properties?.map((o: any) => o.propertyId) ?? [];
        const shouldFilterProperitesByStep = currentStepProperites?.length > 0;
        const foundStepHeader = shouldFilterProperitesByStep ? currentStepProperites.findIndex((pId: string) => pId === header.propertyId) : -1;
        if (foundStepHeader === -1) {
          return;
        }
      }
      if (found) {
        found.headers.push(header);
      } else {
        groups.push({
          group: groupName,
          headers: [header],
        });
      }
    });
    if (groups.length === 0) {
      groups.push({ headers: rowValues });
    }
    return groups;
  }, [rowValues, steps, currentStepIndex]);

  function getPropertyColumnSpan(property: PropertyWithDetails) {
    const columns = PropertyAttributeHelper.getPropertyAttributeValue_Number(property, PropertyAttributeName.Columns);
    if (columns === undefined || isNaN(columns) || (columns < 1 && columns > 12)) {
      return "col-span-12";
    }
    return `col-span-${columns}`;
  }
  function onChange(rowValue: RowValueDto) {
    setHeaders((prev) => {
      return prev.map((f) => {
        if (f.propertyId === rowValue.propertyId) {
          return rowValue;
        }
        return f;
      });
    });
  }

  useEffect(() => {
    const filledCounts: Record<string, number> = {};
    groups.forEach(({ group, headers }) => {
      let groupFilledCount = 0;
      headers.forEach((header) => {
        const isFilled =
          (header.textValue?.trim() !== "" && header.textValue !== undefined) || // Standard text field
          (header.numberValue !== undefined && !isNaN(header.numberValue)) || // Number field
          header.booleanValue !== undefined || // Boolean field
          header.dateValue !== undefined || // Date field
          header.range !== undefined || // Range field
          (header.multiple && header.multiple.length > 0) || // Array (multi-select) field
          (header.media && header.media.length > 0) || // Media (file upload) field
          // Handle multi-text (split by newline)
          (typeof header.textValue === "string" && header.textValue.split("\n").filter((line) => line.trim() !== "").length > 0); // Multi-line text (split by Enter key)

        if (isFilled) {
          groupFilledCount++;
        }
      });

      // Store the count for the group
      filledCounts[group || "default"] = groupFilledCount;
    });

    setFilledFieldsCounts(filledCounts);
  }, [groups]);

  // Create a simple key-value object of all current form values for cascading location properties
  const getAllFormValues = (groupHeaders: RowValueDto[]) => {
    const allValues: { [key: string]: any } = {};
    // Get values from all groups
    groups.forEach(({ headers: groupHeadersInGroup }) => {
      groupHeadersInGroup.forEach(header => {
        if (header.property) {
          const value = header.textValue || header.numberValue || header.dateValue || header.booleanValue;
          if (value) {
            allValues[header.property.name] = value;
          }
        }
      });
    });
    console.log("getAllFormValues result:", allValues);
    return allValues;
  };

  return (
    <>
      {groups.map(({ group, headers }, idx) => {
        return (
          <InputGroup key={idx} totalFields={headers.length} filled={filledFieldsCounts[group || "default"]} title={group ? t(group) : t("shared.details")}  inputGroupClassName={clsx(isDrawer && "px-4")}>
            <div className="grid grid-cols-1 gap-3 sm:grid-cols-12">
              {headers.map((detailValue, idxDetailValue) => {
                return (
                  <div key={detailValue.propertyId} className={clsx("w-full", getPropertyColumnSpan(detailValue.property))}>
                    <RowValueInput
                      className="border-input focus-within:border-foreground focus:border-foreground shadow-none focus:ring-0 focus:outline-none placeholder:text-[#B9BDCA]"
                      ref={rowValueInput}
                      entity={entity}
                      textValue={detailValue.textValue}
                      numberValue={detailValue.numberValue}
                      dateValue={detailValue.dateValue}
                      booleanValue={detailValue.booleanValue}
                      multiple={detailValue.multiple}
                      range={detailValue.range}
                      initialOption={detailValue.selectedOption}
                      selected={detailValue.property}
                      initialMedia={detailValue.media}
                      onChange={(e) => {
                        onChange({
                          ...detailValue,
                          ...RowHelper.updateFieldValueTypeArray(detailValue, e),
                        });
                      }}
                      onChangeOption={(e) => {
                        onChange({
                          ...detailValue,
                          selectedOption: e,
                          textValue: e,
                        });
                      }}
                      onChangeMedia={(media) => {
                        onChange({
                          ...detailValue,
                          media: media as any,
                        });
                        if (media.filter((f) => f.type).length > 0) {
                          onSaveIfAllSet();
                        }
                      }}
                      onChangeMultiple={(e) => {
                        onChange({
                          ...detailValue,
                          multiple: e as any[],
                        });
                      }}
                      onChangeRange={(e) => {
                        onChange({
                          ...detailValue,
                          range: e as any,
                        });
                      }}
                      readOnly={
                        (editing && !detailValue.property.canUpdate) || (item?.id !== undefined && (!editing || !canUpdate)) || detailValue.property?.isReadOnly
                      }
                      autoFocus={idx === 0 && idxDetailValue === 0 && canSubmit}
                      promptFlows={promptFlows ? { prompts: promptFlows, rowId: item?.id } : undefined}
                      allFormValues={getAllFormValues(headers)}
                    />
                    {requiredErrorList?.includes(detailValue.property.title) &&
                      <div className="text-destructive text-sm">
                        Please fill this field
                      </div>}
                  </div>
                );
              })}
              {/* Show parent entities in Default Properties Group */}
              {!group && (
                <>
                  {parentEntities.visible.map((relationship) => (
                    <div key={relationship.id} className="col-span-12">
                      <label htmlFor={relationship.id} className="text-foreground flex justify-between space-x-2 text-xs font-medium">
                        <div className="flex items-center space-x-1">
                          <div className="truncate">
                             <span className="font-bold text-sm text-[#0A0501]">
                            {t(RelationshipHelper.getTitle({ fromEntityId: entity.id, relationship }))}
                            </span>
                            {relationship.required && <span className="ml-1 text-red-500">*</span>}
                          </div>
                        </div>
                      </label>
                      <RelationshipSelector
                        fromEntity={entity}
                        className="mt-1"
                        type="parent"
                        relationship={relationship}
                        relatedRows={relatedRows}
                        onFindEntityRows={onFindEntityRows}
                        allEntities={allEntities}
                        onRemoveRelatedRow={onRemoveRelatedRow}
                        readOnly={item?.id !== undefined && (!editing || !canUpdate)}
                        routes={routes}
                        relationshipRows={relationshipRows}
                        addRelationshipRow={addRelationshipRow}
                        setRelationshipRows={setRelationshipRows}
                      />
                    </div>
                  ))}

                  {isAddingOrEditing && (
                    <AddHiddenRelationshipEntities items={parentEntities.hidden} onClick={parentEntities.onAddParentEntity} type="parent" />
                  )}
                </>
              )}
              {/* Show custom properties in Default Properties Group */}
              {!group && <>{children}</>}
            </div>
          </InputGroup>
        );
      })}
    </>
  );
}

function AddHiddenRelationshipEntities({
  items,
  onClick,
  type,
}: {
  items: EntityRelationshipWithDetails[];
  onClick: (item: EntityRelationshipWithDetails) => void;
  type: "parent" | "child";
}) {
  const { t } = useTranslation();
  return (
    <Fragment>
      {items.length > 0 && (
        <div className="col-span-12 flex flex-wrap items-center">
          {items.map((relationship) => (
            <button
              key={relationship.id}
              type="button"
              onClick={() => onClick(relationship)}
              className="text-muted-foreground bg-secondary/90 m-0.5 w-auto rounded-md px-2 py-1 text-xs font-medium hover:bg-gray-200"
            >
              {t("shared.add")} {type === "parent" ? t(relationship.parent.title) : t(relationship.child.title)}
            </button>
          ))}
        </div>
      )}
    </Fragment>
  );
}

function getVisibleRelatedEntities(
  entityRelationships: EntityRelationshipWithDetails[],
  relatedRows: { relationship: EntityRelationshipWithDetails; rows: RowWithValues[] }[]
) {
  const visible = entityRelationships.filter((f) => !f.hiddenIfEmpty);
  const hidden: EntityRelationshipWithDetails[] = [];

  entityRelationships
    .filter((f) => f.hiddenIfEmpty)
    .forEach((relationship) => {
      const rows = relatedRows.filter((f) => f.relationship.id === relationship.id).flatMap((f) => f.rows);
      if (rows.length > 0) {
        visible.push(relationship);
      } else {
        hidden.push(relationship);
      }
    });

  return {
    visible,
    hidden,
  };
}

export default forwardRef(RowForm);
