import { Fragment, useEffect, useState } from "react";
import { Listbox, Transition } from "@headlessui/react";
import { useTranslation } from "react-i18next";
import clsx from "clsx";
import SelectorIcon from "../../ui/icons/SelectorIcon";
import CheckIcon from "../../ui/icons/CheckIcon";
import { PropertyType } from "~/application/enums/entities/PropertyType";
import { Country, State, City } from "~/utils/api/.server/CountryStateCityApi";

interface Props {
  type: PropertyType.COUNTRY | PropertyType.STATE | PropertyType.CITY;
  className?: string;
  selected: string | undefined;
  onSelected: (item: string) => void;
  // For cascading - parent selections
  countryIso?: string;
  stateIso?: string;
  disabled?: boolean;
}

export default function CascadingLocationSelector({ 
  type, 
  className, 
  selected, 
  onSelected, 
  countryIso, 
  stateIso,
  disabled = false 
}: Props) {
  const { t } = useTranslation();
  const [items, setItems] = useState<(Country | State | City)[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadItems();
  }, [type, countryIso, stateIso]);

  const loadItems = async () => {
    setLoading(true);
    setError(null);
    
    try {
      let url = "";
      
      switch (type) {
        case PropertyType.COUNTRY:
          url = "/api/location/countries";
          break;
        case PropertyType.STATE:
          if (!countryIso) {
            setItems([]);
            setLoading(false);
            return;
          }
          url = `/api/location/states/${countryIso}`;
          break;
        case PropertyType.CITY:
          if (!countryIso || !stateIso) {
            setItems([]);
            setLoading(false);
            return;
          }
          url = `/api/location/cities/${countryIso}/${stateIso}`;
          break;
        default:
          setItems([]);
          setLoading(false);
          return;
      }

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch ${getTypeName()}`);
      }
      
      const data = await response.json();
      setItems(data);
    } catch (err: any) {
      setError(err.message);
      setItems([]);
    } finally {
      setLoading(false);
    }
  };

  const getTypeName = () => {
    switch (type) {
      case PropertyType.COUNTRY:
        return "countries";
      case PropertyType.STATE:
        return "states";
      case PropertyType.CITY:
        return "cities";
      default:
        return "items";
    }
  };

  const getDisplayValue = () => {
    if (loading) return `Loading ${getTypeName()}...`;
    if (error) return `Error loading ${getTypeName()}`;
    if (!selected) return `Select ${getTypeName().slice(0, -1)}`;
    
    const item = items.find(item => getItemValue(item) === selected);
    return item ? getItemName(item) : selected;
  };

  const getItemValue = (item: Country | State | City) => {
    if (type === PropertyType.COUNTRY) {
      return (item as Country).iso2;
    } else if (type === PropertyType.STATE) {
      return (item as State).iso2;
    } else {
      return (item as City).id.toString();
    }
  };

  const getItemName = (item: Country | State | City) => {
    return item.name;
  };

  const getItemDisplay = (item: Country | State | City) => {
    if (type === PropertyType.COUNTRY) {
      const country = item as Country;
      return `${country.emoji} ${country.name}`;
    }
    return item.name;
  };

  return (
    <Listbox value={selected} onChange={onSelected} disabled={disabled || loading}>
      {({ open }) => (
        <>
          <input type="hidden" name={getTypeName().slice(0, -1)} value={selected || ""} readOnly />
          <div className={clsx("relative", className)}>
            <Listbox.Button 
              className={clsx(
                "focus:border-border focus:ring-ring border-border text-foreground bg-background relative w-full cursor-default rounded-md border py-2 pr-10 pl-3 text-left shadow-2xs focus:ring-1 focus:outline-hidden sm:text-sm",
                (disabled || loading) && "opacity-50 cursor-not-allowed"
              )}
            >
              <span className="block truncate">{getDisplayValue()}</span>
              <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                <SelectorIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
              </span>
            </Listbox.Button>

            <Transition
              show={open}
              as={Fragment}
              leave="transition ease-in duration-100"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <Listbox.Options className="border-border bg-background absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md border py-1 text-base shadow-lg focus:outline-hidden sm:text-sm">
                {error ? (
                  <div className="flex justify-center p-2 text-red-500 select-none">
                    {error}
                  </div>
                ) : items.length === 0 ? (
                  <div className="flex justify-center p-2 text-gray-500 select-none">
                    {loading ? `Loading ${getTypeName()}...` : `No ${getTypeName()} available`}
                  </div>
                ) : (
                  <>
                    {items.map((item) => (
                      <Listbox.Option
                        key={getItemValue(item)}
                        className={({ active }) =>
                          clsx(
                            active ? "bg-secondary" : "",
                            "hover:bg-secondary relative cursor-default py-2 pr-9 pl-3 select-none"
                          )
                        }
                        value={getItemValue(item)}
                      >
                        {({ selected, active }) => (
                          <>
                            <div className="flex items-center space-x-2">
                              <span className={clsx(selected ? "font-medium" : "font-normal", "block truncate")}>
                                {getItemDisplay(item)}
                              </span>
                            </div>
                            {selected ? (
                              <span
                                className={clsx(
                                  active ? "text-white" : "text-blue-600",
                                  "absolute inset-y-0 right-0 flex items-center pr-3"
                                )}
                              >
                                <CheckIcon className="h-5 w-5" aria-hidden="true" />
                              </span>
                            ) : null}
                          </>
                        )}
                      </Listbox.Option>
                    ))}
                  </>
                )}
              </Listbox.Options>
            </Transition>
          </div>
        </>
      )}
    </Listbox>
  );
}
