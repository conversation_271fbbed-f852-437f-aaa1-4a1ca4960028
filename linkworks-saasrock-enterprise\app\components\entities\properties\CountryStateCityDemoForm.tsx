import React, { useState } from "react";
import { CountryStateCitySelector } from "./CountryStateCitySelector";

export const CountryStateCityDemoForm: React.FC = () => {
  const [location, setLocation] = useState<{ country?: string; state?: string; city?: string }>({});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    alert(`Selected: ${location.country || "-"} / ${location.state || "-"} / ${location.city || "-"}`);
  };

  return (
    <form onSubmit={handleSubmit} style={{ maxWidth: 400, margin: "2rem auto", padding: 24, border: "1px solid #eee", borderRadius: 8 }}>
      <h2 style={{ marginBottom: 16 }}>Country / State / City Demo</h2>
      <CountryStateCitySelector value={location} onChange={setLocation} />
      <button type="submit" style={{ marginTop: 24, padding: "8px 16px" }}>
        Submit
      </button>
      <div style={{ marginTop: 16, color: "#555" }}>
        <strong>Selected:</strong> {location.country || "-"} / {location.state || "-"} / {location.city || "-"}
      </div>
    </form>
  );
};

export default CountryStateCityDemoForm;
