# Cascading Location Properties Setup

This document explains how to set up and use the new cascading Country, State, and City properties in your Linkworks application.

## Overview

The cascading location properties allow you to create dropdown fields that automatically filter based on parent selections:
- **Country**: Shows all available countries
- **State**: Shows states/regions for the selected country
- **City**: Shows cities for the selected state and country

## API Setup

### 1. Get API Key

1. Visit [Country State City API](https://countrystatecity.in/)
2. Request an API key by filling out [this form](https://forms.gle/ckZ8Gub6jS9sUihJA)
3. You'll receive your API key via email

### 2. Configure Environment Variable

Add your API key to your `.env` file:

```bash
COUNTRY_STATE_CITY_API_KEY=your_actual_api_key_here
```

## Usage

### Creating Cascading Location Properties

1. **Navigate to Entity Properties**
   - Go to Admin → Entities → [Your Entity] → Properties

2. **Create Country Property**
   - Click "Add Property"
   - Select "Country" as the property type
   - Set name (e.g., "country") and title (e.g., "Country")
   - Configure other settings as needed
   - Save the property

3. **Create State Property**
   - Click "Add Property"
   - Select "State" as the property type
   - Set name (e.g., "state") and title (e.g., "State/Region")
   - **Important**: Make sure you have a Country property in the same entity
   - Save the property

4. **Create City Property**
   - Click "Add Property"
   - Select "City" as the property type
   - Set name (e.g., "city") and title (e.g., "City")
   - **Important**: Make sure you have both Country and State properties in the same entity
   - Save the property

### Property Dependencies

The cascading behavior works as follows:

- **State properties** depend on Country properties
- **City properties** depend on both Country and State properties
- When a parent selection changes, child selections are automatically reset

### Data Storage

- **Country**: Stored as ISO2 code (e.g., "US", "CA", "IN")
- **State**: Stored as ISO2 code (e.g., "CA", "TX", "MH")
- **City**: Stored as numeric ID (e.g., "133024")

## API Endpoints

The system creates the following internal API endpoints:

- `GET /api/location/countries` - Get all countries
- `GET /api/location/states/{countryIso}` - Get states for a country
- `GET /api/location/cities/{countryIso}/{stateIso}` - Get cities for a state

## Components

### New Components Added

1. **CascadingLocationSelector** - Individual dropdown component
2. **CascadingLocationGroup** - Wrapper for multiple cascading dropdowns
3. **CascadingLocationInput** - Form input component for row forms

### Property Types Added

- `PropertyType.COUNTRY = 17`
- `PropertyType.STATE = 18`
- `PropertyType.CITY = 19`

## Troubleshooting

### Common Issues

1. **"Unauthorized" Error**
   - Check that your API key is correctly set in the `.env` file
   - Verify the API key is valid by testing it directly with the API

2. **States/Cities Not Loading**
   - Ensure parent properties exist in the same entity
   - Check browser console for API errors
   - Verify the country/state codes are valid

3. **Empty Dropdowns**
   - Some countries may not have states in the API
   - Some states may not have cities in the API
   - This is normal behavior for the external API

### API Limitations

- Free tier has rate limits
- Some geographical regions may have incomplete data
- API requires internet connection

## Example Entity Setup

For a "Customer" entity, you might create:

1. **Country Property**
   - Name: `country`
   - Title: `Country`
   - Type: `Country`

2. **State Property**
   - Name: `state`
   - Title: `State/Province`
   - Type: `State`

3. **City Property**
   - Name: `city`
   - Title: `City`
   - Type: `City`

This will create a form where users first select a country, then see states for that country, then see cities for the selected state.

## Data API Source

This implementation uses the [Country State City API](https://countrystatecity.in/) which provides:
- 250+ countries
- 5000+ states/regions
- 150,000+ cities
- Regular updates
- Free tier available
